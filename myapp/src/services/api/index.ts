// API 基础地址配置
// export const API_BASE_URL = 'http://192.168.200.31:31002';
export const API_BASE_URL = 'http://192.168.200.31:30088';
// export const API_BASE_URL = 'http://192.168.200.31:30005';

// API 路径配置
export const API_PATHS = {
  // 数据目录相关
  DATA_CATEGORY: {
    LIST: '/rule/data-category/list', // 获取数据目录列表
    ADD: '/rule/data-category/add', // 新增数据目录
    UPDATE: '/rule/data-category/update', // 更新数据目录
    UNSTRUCTURED_DATA: '/rule/unstructured-data/page', // 获取非结构化数据列表
    PRE_DELETE: '/rule/data-category/preDelete', // 预删除检查
    DELETE: '/rule/data-category/deleteById', // 正式删除
    UPLOAD: '/rule/unstructured-data/batch-upload', // 上传非结构化数据
    BATCH_DELETE: '/rule/unstructured-data/batch-delete', // 批量删除非结构化数据
    DELETE_IMPORT: '/rule/unstructured-data/delete-import', // 删除导入文件
    CONFIRM_IMPORT: '/rule/unstructured-data/confirm-import', // 确认导入
    CANCEL_IMPORT: '/rule/unstructured-data/cancel-import', // 取消导入
    MINERU_DETAIL: '/rule/unstructured-data/detail', // 查看 Mineru解析文件详情
    STRUCTURED_DATA_TABLE_LIST: '/rule/structured-data/table/list', // 获取结构化数据表格列表
    STRUCTURED_DATA_TABLE_ADD: '/rule/structured-data/table/add', // 新增结构化数据表格
    STRUCTURED_DATA_TABLE_DELETE: '/rule/structured-data/table/delete', // 删除结构化数据表格
    STRUCTURED_DATA_TABLE_EDIT: '/rule/structured-data/table/edit', // 更新结构化数据表格
    STRUCTURED_DATA_TABLE_LIST_FIELD: '/rule/structured-data/table/list-field', // 预览结构化数据已有数据表结构
    STRUCTURED_DATA_CHECK_EXIST_TABLE_STRUCTURE:
      '/rule/structured-data/check-exist-table-structure', // 检查导入数据与已有表结构配置是否对齐
    STRUCTURED_DATA_GET_TABLE_STRUCTURE: '/rule/structured-data/get-table-structure', // 获取结构化数据已有表结构
    STRUCTURED_DATA_UPDATE_TABLE_STRUCTURE: '/rule/structured-data/update-table-structure', // 更新结构化数据表结构
    STRUCTURED_DATA_IMPORT: '/rule/structured-data/import', // 导入结构化数据
    STRUCTURED_DATA_PAGE_FIELD_CONFIG: '/rule/structured-data/page-field-config', // 获取结构化数据字段配置
    STRUCTURED_DATA_PAGE: '/rule/structured-data/page', // 获取结构化数据列表
    STRUCTURED_DATA_EXPORT: '/rule/structured-data/export', // 导出结构化数据
    STRUCTURED_DATA_FIELD_TYPE_CONFIG: '/rule/structured-data/field-type-config', // 获取结构化数据字段类型配置
  },
  // 应用相关
  APP: {
    LIST: '/rule/apppage', // 获取应用列表
    DETAIL: '/rule/appdetail', // 获取应用详情
    CREATE: '/rule/appadd', // 创建应用
    UPDATE: '/rule/appupdate', // 更新应用
    DELETE: '/rule/appdelete', // 删除应用
  },
  // 用户相关
  USER: {
    LOGIN: '/api/login/account', // 登录
    LOGOUT: '/api/login/outLogin', // 退出登录
    CURRENT: '/api/currentUser', // 获取当前用户信息
  },
  // 规则相关
  RULE: {
    LIST: '/api/rule', // 获取规则列表
    CREATE: '/api/rule', // 创建规则
    UPDATE: '/api/rule', // 更新规则
    DELETE: '/api/rule', // 删除规则
  },
};

// 导出各模块API
export * as appApi from './app';
export * as componentApi from './component';
export * as mcpApi from './mcp';
export * as modelApi from './model';
export * from './request';
export * from './types';

// 兼容旧版
export const getApiUrl = (path: string, params?: Record<string, any>): string => {
  let url = `${API_BASE_URL}${path}`;

  if (params) {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    url = `${url}?${queryString}`;
  }

  return url;
};
