import { message } from 'antd';
import { API_BASE_URL } from './index';
import { getAuthorizationHeader, getToken } from '@/utils/auth';

export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
}

export async function request<T = any>(path: string, options: RequestOptions = {}): Promise<T> {
  const { method = 'GET', headers = {}, body, params } = options;

  const url = new URL(`${API_BASE_URL}${path}`);

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        url.searchParams.append(key, String(value));
      }
    });
  }

    // 获取Authorization头
  const authHeader = getAuthorizationHeader();

  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      // 如果有token，自动添加到Authorization头
      ...(authHeader && { 'Authorization': authHeader }),
      ...headers,
    },
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url.toString(), config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || '请求失败');
    }

    return data;
  } catch (error: unknown) {
    if (error instanceof Error) {
      message.error(error.message || '网络错误');
    } else {
      message.error('网络错误');
    }
    throw error;
  }
}

export async function uploadRequest<T = any>(path: string, formData: FormData): Promise<T> {
  // 获取Authorization头
  const authHeader = getAuthorizationHeader();

  try {
    const response = await fetch(`${API_BASE_URL}${path}`, {
      method: 'POST',
      headers: {
        // 如果有token，自动添加到Authorization头
        ...(authHeader && { 'Authorization': authHeader }),
      },
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || '上传失败');
    }

    return data;
  } catch (error: unknown) {
    if (error instanceof Error) {
      message.error(error.message || '上传失败');
    } else {
      message.error('上传失败');
    }
    throw error;
  }
}

/**
 * 下载文件请求
 * @param path - 请求路径
 * @param params - 查询参数
 * @returns Promise<Blob>
 */
export async function downloadRequest(path: string, params?: Record<string, any>): Promise<Blob> {
  // 获取Authorization头
  const authHeader = getAuthorizationHeader();

  const url = new URL(`${API_BASE_URL}${path}`);

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        url.searchParams.append(key, String(value));
      }
    });
  }

  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        // 如果有token，自动添加到Authorization头
        ...(authHeader && { 'Authorization': authHeader }),
      },
    });

    if (!response.ok) {
      throw new Error('下载失败');
    }

    return await response.blob();
  } catch (error: unknown) {
    if (error instanceof Error) {
      message.error(error.message || '下载失败');
    } else {
      message.error('下载失败');
    }
    throw error;
  }
}

/**
 * 创建SSE连接来查询批量上传进度
 * @param path - SSE接口路径
 * @param onMessage - 接收到消息时的回调函数
 * @param onError - 发生错误时的回调函数
 * @param onOpen - 连接打开时的回调函数
 * @returns EventSource实例
 */
export function createSSEConnection(
  path: string,
  onMessage?: (data: any) => void,
  onError?: (error: Event) => void,
  onOpen?: (event: Event) => void,
  onDone?: (event: Event) => void
): EventSource {
  // 获取token
  const token = getToken();

  // 构建带token的URL
  const url = new URL(`${API_BASE_URL}${path}`);
  if (token) {
    url.searchParams.append('token', token);
  }

  const eventSource = new EventSource(url.toString());

  if (onOpen) {
    console.log('createSSEConnection-onOpen', onOpen)
    eventSource.onopen = onOpen;
  }

  if (onMessage) {
    console.log('createSSEConnection-onMessage', onMessage)
    eventSource.onmessage = (event) => {
      try {
        console.log('createSSEConnection-onMessage-event', event)
        const data = JSON.parse(event.data);
        onMessage(data);
        console.log('createSSEConnection-onMessage-data', data)
      } catch (error) {
        console.error('解析SSE消息失败:', error);
        onMessage(event.data);
      }
    };
  }
  if(onDone){
    eventSource.addEventListener('done', () => {
      console.log('EventSource连接结束');
      //关闭连接（只有关闭后才不会一直重连）
      eventSource.close();
    }, false);
  }

  if (onError) {
    eventSource.onerror = onError;
  }

  return eventSource;
}
