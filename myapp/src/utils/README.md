# Token 管理工具

## 概述

本项目实现了完整的token管理功能，包括：

1. **登录成功后自动存储token到localStorage**
2. **所有API请求自动携带token**
3. **退出登录时自动清除token**
4. **统一的token管理工具函数**

## 文件结构

```
src/utils/
├── auth.ts          # Token管理核心工具
├── tokenTest.ts     # Token功能测试工具
└── README.md        # 本文档
```

## 核心功能

### 1. Token存储和获取

```typescript
import { getToken, setToken, removeToken, hasToken } from '@/utils/auth';

// 设置token
setToken('your-token-here');

// 获取token
const token = getToken();

// 检查是否有token
const hasTokenValue = hasToken();

// 清除token
removeToken();
```

### 2. 自动携带token的API请求

所有通过 `src/services/api/request.ts` 中的函数发起的请求都会自动携带token：

- `request()` - 普通HTTP请求
- `uploadRequest()` - 文件上传请求  
- `createSSEConnection()` - SSE连接

### 3. 登录流程

登录成功后，token会自动存储到localStorage：

```typescript
// 在登录页面中
if (success) {
  setToken(result.token);
  // 后续所有请求都会自动携带token
}
```

### 4. 退出登录流程

退出登录时，token会自动清除：

```typescript
// 在退出登录时
await outLogin();
removeToken();
```

## 使用示例

### 登录页面

```typescript
import { setToken } from '@/utils/auth';

const handleSubmit = async (values: API.LoginParams) => {
  try {
    const { success, result } = await login(values);
    if (success) {
      setToken(result.token);
      // 登录成功后的处理...
    }
  } catch (error) {
    // 错误处理...
  }
};
```

### API请求

```typescript
import { request } from '@/services/api/request';

// 这个请求会自动携带token
const data = await request('/api/some-endpoint', {
  method: 'POST',
  body: { someData: 'value' }
});
```

### 退出登录

```typescript
import { removeToken } from '@/utils/auth';

const loginOut = async () => {
  await outLogin();
  removeToken();
  // 跳转到登录页...
};
```

## 测试功能

在开发环境中，可以在浏览器控制台中使用以下命令测试token功能：

```javascript
// 运行完整的token功能测试
testTokenFunctionality();

// 或者单独测试各个功能
window.getToken();           // 获取token
window.setToken('test');     // 设置token
window.hasToken();           // 检查是否有token
window.removeToken();        // 清除token
```

## 技术实现

### Token存储

- 使用localStorage存储token
- Token键名：`'token'`
- 格式：Bearer Token

### 自动携带机制

1. **HTTP请求**：在请求头中添加 `Authorization: Bearer ${token}`
2. **文件上传**：在请求头中添加 `Authorization: Bearer ${token}`
3. **SSE连接**：在URL参数中添加 `token=${token}`

### 安全性

- Token存储在localStorage中，页面刷新后仍然有效
- 退出登录时立即清除token
- 所有API请求都会验证token的有效性

## 注意事项

1. **Token格式**：确保后端API接受 `Bearer Token` 格式
2. **CORS配置**：确保后端允许 `Authorization` 请求头
3. **Token过期**：建议实现token过期处理机制
4. **安全性**：在生产环境中考虑使用更安全的token存储方式

## 扩展功能

可以根据需要扩展以下功能：

1. **Token过期处理**：自动刷新token
2. **多设备登录**：管理多个设备的token
3. **Token加密存储**：提高安全性
4. **自动登录**：基于token的自动登录功能 
