/**
 * Token管理工具
 */

const TOKEN_KEY = 'token';

/**
 * 获取token
 */
export const getToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * 设置token
 */
export const setToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 清除token
 */
export const removeToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
};

/**
 * 检查是否有token
 */
export const hasToken = (): boolean => {
  return !!getToken();
};

/**
 * 获取Authorization头
 */
export const getAuthorizationHeader = (): string | null => {
  const token = getToken();
  return token ? `Bearer ${token}` : null;
};
