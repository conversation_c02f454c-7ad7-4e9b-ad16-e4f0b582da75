/**
 * Token功能测试工具
 */
import { getToken, setToken, removeToken, hasToken, getAuthorizationHeader } from './auth';

/**
 * 测试token功能
 */
export const testTokenFunctionality = () => {
  console.log('=== Token功能测试开始 ===');

  // 测试初始状态
  console.log('1. 初始状态检查:');
  console.log('   - hasToken():', hasToken());
  console.log('   - getToken():', getToken());
  console.log('   - getAuthorizationHeader():', getAuthorizationHeader());

  // 测试设置token
  console.log('\n2. 设置token测试:');
  const testToken = 'test-token-12345';
  setToken(testToken);
  console.log('   - 设置token后 hasToken():', hasToken());
  console.log('   - 设置token后 getToken():', getToken());
  console.log('   - 设置token后 getAuthorizationHeader():', getAuthorizationHeader());

  // 测试清除token
  console.log('\n3. 清除token测试:');
  removeToken();
  console.log('   - 清除token后 hasToken():', hasToken());
  console.log('   - 清除token后 getToken():', getToken());
  console.log('   - 清除token后 getAuthorizationHeader():', getAuthorizationHeader());

  console.log('=== Token功能测试结束 ===');
};

/**
 * 在浏览器控制台中运行测试
 */
if (typeof window !== 'undefined') {
  // 将测试函数挂载到window对象，方便在控制台调用
  (window as any).testTokenFunctionality = testTokenFunctionality;
}
