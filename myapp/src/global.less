html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.custom-header {
  background: none !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  height: 34px !important;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}
.ant-pro-layout .ant-pro-layout-content {
  padding: 0;
  background: #fff;
}

.ant-pro-layout-content {
  position: relative;
  z-index: 1;
}
.ant-pro-layout-content::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 130px;
  background: url('/layout_bgcolor.png') no-repeat right top;
  background-size: 100% 130px;
  z-index: 1;
  pointer-events: none;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.ant-pro-layout .ant-pro-sider-logo {
  padding: 18px;
  border: 0;
}

.ant-pro-layout .ant-pro-sider-logo > a > img {
  width: 34px;
  height: 34px;
}

.ant-pro-layout .ant-pro-sider-logo > a > h1 {
  white-space: pre-line;
  height: auto;
  line-height: 18px;
  // text-align: center;
}
// .ant-pro-layout .ant-pro-sider-logo>a>img{
//   width: 34px;
//   height: 34px
// }

.ant-menu-inline .ant-menu-item {
  height: 50px;
}
.ant-pro-base-menu-inline .ant-pro-base-menu-inline-item-icon,
.ant-pro-base-menu-inline .ant-pro-base-menu-inline-item-icon:hover {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  // min-height: 50px !important;
}

.ant-pro-base-menu-inline .ant-pro-base-menu-inline-item-icon img {
  width: 24px !important;
  height: 24px !important;
  max-height: 100% !important;
  display: block !important;
  margin: 0 auto !important;
}

.ant-menu-item-selected,
.ant-menu-item-active {
  background: rgba(10, 172, 121, 0.06) !important;
}

// 子菜单样式
.ant-menu-inline .ant-menu-submenu {
  .ant-menu-submenu-title {
    height: 50px;
    line-height: 50px;
    padding-left: 24px !important;
    margin: 0;
    font-weight: 500;
    color: #191919;
    display: flex;
    align-items: center;

    // 确保与其他菜单项对齐
    .ant-menu-title-content {
      display: flex;
      align-items: center;
      width: 100%;
    }
  }

  .ant-menu-submenu-arrow {
    right: 16px;
    color: #939caf;
  }
}

// 确保有子菜单的菜单项与普通菜单项对齐
.ant-menu-inline .ant-menu-submenu > .ant-menu-submenu-title {
  height: 50px !important;
  line-height: 50px !important;
  padding-left: 24px !important;
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;

  .ant-menu-title-content {
    display: flex !important;
    align-items: center !important;
    flex: 1 !important;
  }

  .ant-menu-submenu-arrow {
    margin-left: auto !important;
  }
}

// 子菜单项样式
.ant-menu-inline .ant-menu-submenu .ant-menu-item {
  height: 44px;
  line-height: 44px;
  padding-left: 56px !important;
  margin: 0;
  background: transparent;
  border-radius: 0;
  display: flex;
  align-items: center;

  &:hover {
    background: rgba(10, 172, 121, 0.04) !important;
  }

  &.ant-menu-item-selected {
    background: rgba(10, 172, 121, 0.06) !important;
    color: #0aac79;
    font-weight: 500;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #0aac79;
    }
  }
}

// 子菜单展开时的样式
.ant-menu-inline .ant-menu-submenu.ant-menu-submenu-open {
  .ant-menu-submenu-title {
    color: #0aac79;
    font-weight: 500;
  }

  .ant-menu-submenu-arrow {
    color: #0aac79;
  }
}

.ant-pro-layout .ant-layout-sider.ant-pro-sider {
  background: #f9fafc;
}

.ant-pro-layout .ant-pro-layout-content {
  padding: 0;
  background: #fff;
}
.ant-pro-page-container-children-container {
  position: relative;
  z-index: 3;
  background: transparent;
  padding: 20px;
}

.ant-pro-page-container .ant-pro-page-container-warp-page-header {
  border: 0;
  border: none;
  padding: 0;
  padding-block-start: 0;
  padding-block-end: 0;
  border-width: 0px 0px 1px 0px;
  border-style: solid;
  border-color: #eaeff3;
  padding-left: 20px;
}

.ant-pro-page-container .ant-pro-page-container-warp-page-header .ant-page-header-heading {
  padding-block-start: 0;
  height: 70px;
}

.ant-pro-page-container-warp-page-header {
  position: relative;
  z-index: 4;
  background: transparent;
  padding-top: 56px;
}

.ant-pro-layout .ant-pro-layout-bg-list {
  background: none;
}

// 悬浮设置按钮样式
.ant-pro-setting-drawer-handle {
  z-index: 10 !important;
  position: fixed; // 或 absolute，确保其脱离文档流
}

.ant-switch.ant-switch-checked {
  background-color: #0aac79;
}
.ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
  background-color: #0aac79;
}

.ant-segmented {
  padding: 4px;
}

.ant-segmented .ant-segmented-item-label {
  // width: 60px;
  // height: 32px;
  // line-height: 32px;
  padding: 0;
  font-weight: 700;
}
.ant-card .ant-card-body {
  background: #f6f7fb;
  border-radius: 8px;
}

.ant-switch.ant-switch-small .ant-switch-handle {
  width: 20px;
  height: 20px;
}
.ant-switch.ant-switch-small.ant-switch-checked .ant-switch-handle {
  inset-inline-start: calc(100% - 22px);
}
.ant-switch.ant-switch-small {
  width: 38px;
  height: 24px;
}
.ant-card .ant-card-actions > li {
  margin: 0;
}
.ant-card .ant-card-meta-description div:first-child {
  font-size: 12px;
  color: #939caf;
}
.ant-card .ant-card-meta-title {
  font-size: 16px;
  color: #191919;
}

// 搜索框样式
.search-filter-input.ant-input-outlined:hover {
  border-color: #0aac79;
}

.search-filter-input.ant-input-outlined:focus-within {
  box-shadow: 0 0 0 2px rgba(10, 172, 121, 0.06);
}

.search-filter-input.ant-input-outlined:focus,
.search-filter-input.ant-input-outlined:focus-within {
  border-color: #0aac79;
}

// 应用状态选择框样式
.search-filter-select {
  .ant-select-selector {
    height: 40px !important;
    display: flex;
    align-items: center;
    border-radius: 6px;
    background: #fff;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  &:hover .ant-select-selector {
    border-color: #0aac79 !important;
  }

  &.ant-select-focused .ant-select-selector,
  .ant-select-selector:focus,
  .ant-select-selector:focus-within {
    border-color: #0aac79 !important;
    box-shadow: 0 0 0 2px rgba(10, 172, 121, 0.06) !important;
  }
}

// 折叠
.ant-pro-sider-collapsed-button {
  position: absolute;
  inset-block-start: 30%;
  z-index: 101;
  width: 16px;
  height: 44px;
  text-align: center;
  border-radius: 8px;
  inset-inline-end: -8px;
  transition: transform 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.25);
  background-color: #ffffff;
  background: #eaeff3;
}

.ant-pro-sider-links {
  display: flex;
  flex-direction: column-reverse;
  padding-bottom: 20px;
}

.ant-pro-sider-links a:not(.custom-link) {
  margin-bottom: 20px;
}

.ant-pro-layout .ant-pro-sider .ant-layout-sider-children {
  // padding-bottom: 33vh;
}
